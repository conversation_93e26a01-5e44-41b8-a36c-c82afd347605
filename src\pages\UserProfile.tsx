import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import Navbar from "../components/Navbar";
import { userService } from "../services/api/user.service";
import { showErrorToast } from "../utils/showErrorToast";
import { showSuccessToast } from "../utils/showSuccessToast";
import UserWarStats from "../components/analytics/UserWarStats.tsx";
import { FaCrown, FaEnvelope, FaCoins } from "react-icons/fa";
import { User } from "../types/user";
import { Region } from "../types/region";
import { State } from "../types/state";
import useUserDataStore from "../store/useUserDataStore";
import useChatStore from "../store/useChatStore";
import { chatService } from "../services/api/chat.service";
import ChatInterface from "../components/chat/ChatInterface";

export default function UserProfile() {
  const { id } = useParams<{ id: string }>();
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [region, setRegion] = useState<Region | null>(null);
  const [userState, setUserState] = useState<State | null>(null);
  const { userData: currentUser, loading: userDataLoading, fetchUserData } = useUserDataStore();
  const { connect, createChat, setActiveChat } = useChatStore();

  const [transferAmount, setTransferAmount] = useState<number>(100);
  const [showTransferModal, setShowTransferModal] = useState<boolean>(false);
  const [isCreatingChat, setIsCreatingChat] = useState<boolean>(false);
  const [isChatOpen, setIsChatOpen] = useState<boolean>(false);

  // Check if the user is viewing their own profile
  const isOwnProfile = currentUser?.id && id && currentUser.id.toString() === id;

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        if (!id) return;
        const userData = await userService.getUserById(id);
        setProfile(userData);

        if (userData?.region) {
          setRegion(userData.region);
        }
        // Check if user has a state through their region
        if (userData?.region?.state) {
          setUserState(userData.region.state);
        }
      } catch (error) {
        showErrorToast(error || "Failed to load user profile");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchUserProfile();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-neonBlue text-xl">Loading profile...</div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-900">
        <Navbar />
        <div className="flex flex-col items-center justify-center h-[calc(100vh-4rem)]">
          <div className="text-red-500 text-xl mb-4">User not found</div>
          <Link
            to="/home"
            className="bg-neonBlue hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  // Transfer modal component
  const TransferMoneyModal = () => {
    const [amount, setAmount] = useState<number>(transferAmount);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

    const handleTransfer = async () => {
      try {
        setIsSubmitting(true);
        // Get the current user ID from auth context/store
        const currentUserId = currentUser.id; // Replace with your actual method to get logged-in user ID

        // Using the userService function to send money with the correct DTO structure
        await userService.sendMoney({
          fromUserId: currentUserId,
          toUserId: Number(id), // Convert string ID to number
          amount: amount,
        });

        fetchUserData(true);

        showSuccessToast("Money sent successfully!");
        setShowTransferModal(false);
      } catch (error) {
        // Handle specific error codes from the backend
        if (error?.response?.status === 409) {
          showErrorToast("Insufficient funds for this transfer");
        } else if (error?.response?.status === 400) {
          showErrorToast("Invalid transfer request");
        } else if (error?.response?.status === 404) {
          showErrorToast("User not found or not active");
        } else {
          showErrorToast(error?.message || "Failed to send money");
        }
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
          <h2 className="text-xl font-semibold text-white mb-4">
            Send Money to {profile?.username}
          </h2>

          <div className="mb-4">
            <label
              htmlFor="transferAmount"
              className="block text-gray-400 mb-2"
            >
              Amount
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <span className="text-yellow-400">💰</span>
              </div>
              <input
                id="transferAmount"
                type="number"
                min="0.01"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                className="bg-gray-700 text-white rounded-md py-2 pl-10 pr-4 w-full focus:outline-none focus:ring-2 focus:ring-neonBlue"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowTransferModal(false)}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-md"
            >
              Cancel
            </button>
            <button
              onClick={handleTransfer}
              disabled={amount <= 0 || isSubmitting}
              className={`bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center ${
                amount <= 0 || isSubmitting
                  ? "opacity-50 cursor-not-allowed"
                  : ""
              }`}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                  Processing...
                </>
              ) : (
                <>
                  <span className="text-yellow-400">💰</span>
                  Send Money
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const handleSendMessage = async () => {
    if (!profile || !currentUser || isCreatingChat) return;

    setIsCreatingChat(true);
    try {
      // Create or find direct chat with this user
      const chat = await createChat({
        type: 'direct',
        participantIds: [profile.id],
      });

      // Set as active chat
      setActiveChat(chat.id);

      // Open chat interface immediately
      setIsChatOpen(true);

    } catch (error: any) {
      console.error('Failed to create chat:', error);
      showErrorToast(error.message || 'Failed to create chat');
    } finally {
      setIsCreatingChat(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <Navbar />

      {/* Own Profile Notice */}
      {isOwnProfile && (
        <div className="bg-neonBlue/10 border-b border-neonBlue/30">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="text-neonBlue mr-2">ℹ️</div>
                <span className="text-white">
                  This is your public profile that others can see.
                </span>
              </div>
              <Link
                to="/profile"
                className="bg-neonBlue hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Edit Profile
              </Link>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row items-center md:items-start">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center text-4xl text-neonBlue mb-4 md:mb-0">
              {profile?.username?.charAt(0).toUpperCase() || "U"}
            </div>
            <div className="md:ml-6 text-center md:text-left flex-grow">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                <div>
                  <h1 className="text-3xl font-bold text-neonBlue">
                    {profile?.username}
                  </h1>
                  <p className="text-gray-400">
                    Member since{" "}
                    {new Date(profile?.createdAt).toLocaleDateString()}
                  </p>
                  {profile?.aboutMe && (
                    <p className="text-white mt-2">{profile.aboutMe}</p>
                  )}
                </div>

                {/* Premium Badge */}
                {profile?.isPremium && (
                  <div className="mt-2 md:mt-0 flex items-center justify-center md:justify-end">
                    <div className="bg-yellow-900/30 border border-yellow-500 text-yellow-400 px-4 py-2 rounded-md flex items-center">
                      <FaCrown className="mr-2" />
                      <span className="font-medium">Premium User</span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Level</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.level || 1}
                  </p>
                </div>
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Strength</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.strength || 0}
                  </p>
                </div>
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Intelligence</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.intelligence || 0}
                  </p>
                </div>
                <div className="bg-gray-700 rounded-lg p-3 text-center">
                  <p className="text-gray-400 text-sm">Endurance</p>
                  <p className="text-white text-xl font-bold">
                    {profile?.endurance || 0}
                  </p>
                </div>
              </div>

              {/* Action Buttons - Only show for other users' profiles */}
              {!isOwnProfile && (
                <div className="mt-4 flex flex-wrap gap-2 justify-center md:justify-start">
                  <button
                    className={`bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center ${
                      isCreatingChat ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    onClick={handleSendMessage}
                    disabled={isCreatingChat}
                  >
                    {isCreatingChat ? (
                      <>
                        <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                        Creating Chat...
                      </>
                    ) : (
                      <>
                        <FaEnvelope className="mr-2" />
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                  <button
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center"
                    onClick={() => setShowTransferModal(true)}
                  >
                    <span className="text-yellow-400">💰</span>
                    <span>Send Money</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Other Information - 2 Columns Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* State Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">State</h2>

            {userState ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {userState.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {userState.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader: {userState.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <Link
                  to={`/states/${userState.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View State Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  This user doesn't belong to any state.
                </p>
              </div>
            )}
          </div>

          {/* Party Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Party</h2>

            {profile?.leadingParty ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {profile.leadingParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.leadingParty.name}
                    </h3>
                    <p className="text-gray-400">Leader: {profile.username}</p>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.leadingParty.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View Party Details
                </Link>
              </div>
            ) : profile?.memberOfParty ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {profile.memberOfParty.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">
                      {profile.memberOfParty.name}
                    </h3>
                    <p className="text-gray-400">
                      Leader:{" "}
                      {profile.memberOfParty.leader?.username || "Unknown"}
                    </p>
                  </div>
                </div>

                <Link
                  to={`/party/${profile.memberOfParty.id}`}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded block text-center"
                >
                  View Party Details
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">
                  This user doesn't belong to any party.
                </p>
              </div>
            )}
          </div>

          {/* Region Information */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Region</h2>
            {region ? (
              <div>
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center text-2xl text-neonBlue">
                    {region.name.charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-white">{region.name}</h3>
                    <p className="text-gray-400">Population: {region?.population?.toLocaleString()}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Resources:</span>
                    <span className="text-white">
                      {region.resources ? (
                        <div className="text-sm">
                          {Object.entries(region.resources).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="text-gray-400">{key}:</span>
                              <span className="text-white">{typeof value === 'object' ? value.current : value}</span>
                            </div>
                          ))}
                        </div>
                      ) : 'None'}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">You don't have a region assigned yet.</p>
              </div>
            )}
          </div>

          {/* War Statistics */}
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-white mb-4">
              War Statistics
            </h2>
            {/* <UserWarStats userId={id} /> */}
          </div>
        </div>
      </div>
          {showTransferModal && <TransferMoneyModal />}

          {/* Chat Interface Modal */}
          <ChatInterface
            isOpen={isChatOpen}
            onClose={() => setIsChatOpen(false)}
          />
    </div>
  );
}
